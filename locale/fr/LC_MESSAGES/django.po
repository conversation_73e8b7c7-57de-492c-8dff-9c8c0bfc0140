# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-23 20:56+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: feed/models.py:94
#, fuzzy
#| msgid "Activity Feed"
msgid "Active"
msgstr "Flux d'Activité"

#: feed/models.py:95
msgid "Draft"
msgstr ""

#: feed/models.py:96
msgid "Closed"
msgstr ""

#: feed/models.py:97
msgid "On-Hold"
msgstr ""

#: feed/models.py:98
msgid "Archived"
msgstr ""

#: feed/models.py:99
msgid "Reviewing"
msgstr ""

#: feed/models.py:100
msgid "Deleted"
msgstr ""

#: feed/models.py:298 templates/applicant_dev.html:1098
msgid "New"
msgstr ""

#: feed/models.py:299 templates/applicant_dev.html:1099
msgid "Review #1"
msgstr ""

#: feed/models.py:300 templates/applicant_dev.html:1100
msgid "Review #2"
msgstr ""

#: feed/models.py:301 templates/applicant_dev.html:1101
msgid "Review #3"
msgstr ""

#: feed/models.py:302 templates/applicant_dev.html:1102
msgid "Review #4"
msgstr ""

#: feed/models.py:303 templates/applicant_dev.html:1103
msgid "Review #5"
msgstr ""

#: feed/models.py:304 templates/applicant_dev.html:1104
msgid "Ready for Decision"
msgstr ""

#: feed/models.py:305 templates/applicant_dev.html:1105
msgid "Eliminated"
msgstr ""

#: feed/models.py:306 templates/applicant_dev.html:1106
msgid "Offer Made"
msgstr ""

#: feed/models.py:307 templates/applicant_dev.html:1107
msgid "Candidate Accepted"
msgstr ""

#: feed/models.py:308 templates/jobs.html:142
msgid "Hired"
msgstr ""

#: feed/models.py:309 templates/applicant_dev.html:1108
msgid "Candidate Rejected"
msgstr ""

#: feed/models.py:348
msgid "Phone Call"
msgstr ""

#: feed/models.py:349
msgid "Video Call"
msgstr ""

#: feed/models.py:350
msgid "Online Interview"
msgstr ""

#: feed/models.py:351
msgid "Technical Assessment"
msgstr ""

#: feed/models.py:352
msgid "Final Interview"
msgstr ""

#: feed/models.py:353
msgid "Face to Face Interview"
msgstr ""

#: feed/models.py:354
msgid "Office Visit"
msgstr ""

#: feed/models.py:355 templates/workloupe_platform.html:107
msgid "Other"
msgstr ""

#: feed/views.py:154
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr ""

#: feed/views.py:195
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr ""

#: feed/views.py:225
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr ""

#: feed/views.py:249
msgid "New comment on application of"
msgstr ""

#: feed/views.py:251
msgid "New comment on application ID"
msgstr ""

#: feed/views.py:267
msgid "now"
msgstr ""

#: feed/views.py:270
msgid "1 minute ago"
msgstr ""

#: feed/views.py:273
msgid "minutes ago"
msgstr ""

#: feed/views.py:276
msgid "1 hour ago"
msgstr ""

#: feed/views.py:280
msgid "hours ago"
msgstr ""

#: feed/views.py:283
msgid "yesterday"
msgstr ""

#: feed/views.py:287
msgid "days ago"
msgstr ""

#: feed/views.py:290
msgid "last week"
msgstr ""

#: feed/views.py:294
msgid "weeks ago"
msgstr ""

#: feed/views.py:297
#, fuzzy
#| msgid "Month"
msgid "last month"
msgstr "Mois"

#: feed/views.py:301
msgid "months ago"
msgstr ""

#: feed/views.py:306
msgid "last year"
msgstr ""

#: feed/views.py:308
msgid "years ago"
msgstr ""

#: feed/views.py:1038
msgid "Profile photo changed successfully!"
msgstr ""

#: feed/views.py:1043
msgid "Please select a photo."
msgstr ""

#: feed/views.py:1852
#, python-format
msgid "Language changed to %(language)s"
msgstr ""

#: feed/views.py:1856
msgid "Invalid language selection"
msgstr ""

#: feed/views.py:1885 templates/feed.html:17
msgid "Dashboard"
msgstr "Tableau de Bord"

#: feed/views.py:2075
msgid "Invitation mail sent successfully!"
msgstr ""

#: feed/views.py:2078 feed/views.py:2221
msgid "Failed to send the invitation. Please check the form."
msgstr ""

#: feed/views.py:2105
msgid "Passwords do not match."
msgstr ""

#: feed/views.py:2136
msgid "No employer found to associate with this account."
msgstr ""

#: feed/views.py:2146
msgid "Registration completed successfully! You can now log in."
msgstr ""

#: feed/views.py:2150
#, python-format
msgid "Error creating account: %(error)s"
msgstr ""

#: feed/views.py:2176
msgid "Access denied."
msgstr ""

#: feed/views.py:2203
msgid "Invitation sent successfully!"
msgstr ""

#: feed/views.py:2232
msgid "User removed successfully!"
msgstr ""

#: feed/views.py:2245
msgid "User status changed successfully!"
msgstr ""

#: feed/views.py:2397
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr ""

#: feed/views.py:2401
msgid "Invalid request method."
msgstr ""

#: feed/views.py:4646
msgid "Image URL and employer ID are required"
msgstr ""

#: feed/views.py:4678
#, python-format
msgid "Failed to remove image: %(error)s"
msgstr ""

#: feed/views.py:4683
msgid "Invalid request method"
msgstr ""

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr ""

#: templates/applicant_dev.html:89 templates/applicant_dev.html:870
msgid "Schedule Interview"
msgstr ""

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr ""

#: templates/applicant_dev.html:114
#, fuzzy
#| msgid "Dashboard"
msgid "Dashboard & AI"
msgstr "Tableau de Bord"

#: templates/applicant_dev.html:123
msgid "Candidate Background"
msgstr ""

#: templates/applicant_dev.html:132 templates/applicant_dev.html:565
#: templates/applicant_dev.html:583
msgid "Resume"
msgstr ""

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr ""

#: templates/applicant_dev.html:150 templates/applicant_dev.html:785
msgid "Internal Comments"
msgstr ""

#: templates/applicant_dev.html:159 templates/applicant_dev.html:685
msgid "Emails"
msgstr ""

#: templates/applicant_dev.html:167 templates/applicant_dev.html:854
msgid "Job Details"
msgstr ""

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr ""

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr ""

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr ""

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr ""

#: templates/applicant_dev.html:272
msgid "Candidate Summary"
msgstr ""

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr ""

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr ""

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr ""

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr ""

#: templates/applicant_dev.html:327
msgid "good match"
msgstr ""

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr ""

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr ""

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr ""

#: templates/applicant_dev.html:363
msgid "AI Analysis Available"
msgstr ""

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr ""

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr ""

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr ""

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr ""

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr ""

#: templates/applicant_dev.html:460
msgid "Candidate Facts"
msgstr ""

#: templates/applicant_dev.html:467
msgid "Applied Position"
msgstr ""

#: templates/applicant_dev.html:473
msgid "Candidate's Address"
msgstr ""

#: templates/applicant_dev.html:476 templates/applicant_dev.html:499
#: templates/applicant_dev.html:506 templates/applicant_dev.html:529
#: templates/applicant_dev.html:536 templates/people.html:119
#: templates/people.html:126 templates/people.html:133
msgid "Not analyzed"
msgstr ""

#: templates/applicant_dev.html:480 templates/people.html:61
#, fuzzy
#| msgid "Applicants"
msgid "Application Date"
msgstr "Candidats"

#: templates/applicant_dev.html:486
#, fuzzy
#| msgid "Applicants"
msgid "Application Portal"
msgstr "Candidats"

#: templates/applicant_dev.html:496
#, fuzzy
#| msgid "Create Job"
msgid "Latest/Current Position"
msgstr "Créer un Emploi"

#: templates/applicant_dev.html:503
msgid "Latest/Current Employer"
msgstr ""

#: templates/applicant_dev.html:510 templates/jobs.html:69
#: templates/manage_permissions.html:117 templates/manage_permissions.html:264
#: templates/people.html:41 templates/people.html:89
#: templates/published_job_details.html:279
msgid "Status"
msgstr ""

#: templates/applicant_dev.html:516
#, fuzzy
#| msgid "Applicants"
msgid "Application ID"
msgstr "Candidats"

#: templates/applicant_dev.html:526
msgid "Total Experience"
msgstr ""

#: templates/applicant_dev.html:533
msgid "Education Level"
msgstr ""

#: templates/applicant_dev.html:540
msgid "Notice Period"
msgstr ""

#: templates/applicant_dev.html:546
#, fuzzy
#| msgid "Applicants"
msgid "Last Communication Date"
msgstr "Candidats"

#: templates/applicant_dev.html:551 templates/applicant_dev.html:722
msgid "No emails found."
msgstr ""

#: templates/applicant_dev.html:570
msgid "Uploaded on"
msgstr ""

#: templates/applicant_dev.html:588
msgid "Open in New Tab"
msgstr ""

#: templates/applicant_dev.html:593
msgid "Download"
msgstr ""

#: templates/applicant_dev.html:619
msgid "PDF Preview Not Available"
msgstr ""

#: templates/applicant_dev.html:620
msgid ""
"Your browser doesn't support PDF preview. Please download the file to view "
"it."
msgstr ""

#: templates/applicant_dev.html:627
msgid "Download PDF"
msgstr ""

#: templates/applicant_dev.html:644
#, fuzzy
#| msgid "Applicants"
msgid "Application Stages"
msgstr "Candidats"

#: templates/applicant_dev.html:661
msgid "Started on:"
msgstr ""

#: templates/applicant_dev.html:673
msgid "No stages available for this application."
msgstr ""

#: templates/applicant_dev.html:691
msgid "Email History"
msgstr ""

#: templates/applicant_dev.html:701
msgid "From:"
msgstr ""

#: templates/applicant_dev.html:702
msgid "To:"
msgstr ""

#: templates/applicant_dev.html:705 templates/applicant_dev.html:1145
msgid "Subject:"
msgstr ""

#: templates/applicant_dev.html:729 templates/applicant_dev.html:775
msgid "Send Email"
msgstr ""

#: templates/applicant_dev.html:744
msgid "Subject"
msgstr ""

#: templates/applicant_dev.html:754 templates/published_job_details.html:443
msgid "Email Body"
msgstr ""

#: templates/applicant_dev.html:797
msgid "Add a comment"
msgstr ""

#: templates/applicant_dev.html:803
msgid "Add your comment here..."
msgstr ""

#: templates/applicant_dev.html:808
msgid "Post Comment"
msgstr ""

#: templates/applicant_dev.html:841
msgid "No comments yet. Be the first to comment!"
msgstr ""

#: templates/applicant_dev.html:876 templates/feed.html:302
msgid "Event Title"
msgstr ""

#: templates/applicant_dev.html:887 templates/feed.html:312
msgid "Event Type"
msgstr ""

#: templates/applicant_dev.html:893 templates/feed.html:318
msgid "Select an event type"
msgstr ""

#: templates/applicant_dev.html:901 templates/feed.html:326
#: templates/manage_permissions.html:49
msgid "Recruiters"
msgstr ""

#: templates/applicant_dev.html:906 templates/feed.html:331
msgid "Select one or many recruiters"
msgstr ""

#: templates/applicant_dev.html:916 templates/feed.html:384
#: templates/people.html:31 templates/people.html:88 templates/profile.html:74
#: templates/profile.html:115
msgid "Position"
msgstr ""

#: templates/applicant_dev.html:933 templates/feed.html:406
msgid "Candidate"
msgstr ""

#: templates/applicant_dev.html:949
msgid "Date"
msgstr ""

#: templates/applicant_dev.html:958 templates/feed.html:420
msgid "Start Time"
msgstr ""

#: templates/applicant_dev.html:964 templates/feed.html:425
msgid "End Time"
msgstr ""

#: templates/applicant_dev.html:971 templates/feed.html:431
msgid "Meeting Link"
msgstr ""

#: templates/applicant_dev.html:984 templates/feed.html:445
msgid "Generate Mirotalk Link"
msgstr ""

#: templates/applicant_dev.html:999 templates/feed.html:460
msgid "Inform invitees by E-mail"
msgstr ""

#: templates/applicant_dev.html:1004 templates/feed.html:465
msgid "Color"
msgstr ""

#: templates/applicant_dev.html:1006 templates/feed.html:467
msgid "Blue"
msgstr ""

#: templates/applicant_dev.html:1007 templates/feed.html:468
msgid "Light Blue"
msgstr ""

#: templates/applicant_dev.html:1008 templates/feed.html:469
msgid "Purple"
msgstr ""

#: templates/applicant_dev.html:1009 templates/feed.html:470
msgid "Pink"
msgstr ""

#: templates/applicant_dev.html:1016 templates/applicant_dev.html:1173
#: templates/create_job_template.html:205 templates/feed.html:476
#: templates/job_details.html:134 templates/manage_permissions.html:413
#: templates/manage_permissions.html:463 templates/profile.html:125
#: templates/profile.html:152 templates/profile.html:184
#: templates/published_job_details.html:380
#: templates/published_job_details.html:465
#: templates/published_job_details.html:686
msgid "Cancel"
msgstr ""

#: templates/applicant_dev.html:1019 templates/feed.html:479
msgid "Save Event"
msgstr ""

#: templates/applicant_dev.html:1078
#, fuzzy
#| msgid "Applicants"
msgid "Change Application Status"
msgstr "Candidats"

#: templates/applicant_dev.html:1096
#, fuzzy
#| msgid "Search applicants..."
msgid "New Status"
msgstr "Rechercher des candidats..."

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "Internal Notes"
msgstr ""

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "(visible only to recruiters)"
msgstr ""

#: templates/applicant_dev.html:1126
msgid "Notify candidate about this status change via email"
msgstr ""

#: templates/applicant_dev.html:1132
msgid "Email Message"
msgstr ""

#: templates/applicant_dev.html:1132
msgid "(will be included in the email to the candidate)"
msgstr ""

#: templates/applicant_dev.html:1143
msgid "Email Preview"
msgstr ""

#: templates/applicant_dev.html:1145
msgid "Your application status has been updated"
msgstr ""

#: templates/applicant_dev.html:1175
msgid "Save Change"
msgstr ""

#: templates/careers_page.html:10
msgid "Career Page Setup"
msgstr ""

#: templates/careers_page.html:11
msgid "Choose the integration method that best suits your needs"
msgstr ""

#: templates/careers_page.html:23
#, fuzzy
#| msgid "Locations"
msgid "RSS Feed Integration"
msgstr "Emplacements"

#: templates/careers_page.html:24
msgid ""
"Already have your own careers page? Get our RSS feed to sync job listings"
msgstr ""

#: templates/careers_page.html:26
msgid "Quick Setup"
msgstr ""

#: templates/careers_page.html:27
msgid "Auto Sync"
msgstr ""

#: templates/careers_page.html:32
msgid "Get RSS Feed"
msgstr ""

#: templates/careers_page.html:40
msgid "Recommended"
msgstr ""

#: templates/careers_page.html:45
msgid "Full HTML Page"
msgstr ""

#: templates/careers_page.html:46
msgid "Let us manage your entire careers page with our professional template"
msgstr ""

#: templates/careers_page.html:48
#, fuzzy
#| msgid "Profile"
msgid "Professional"
msgstr "Profil"

#: templates/careers_page.html:49
msgid "Customizable"
msgstr ""

#: templates/careers_page.html:54
#, fuzzy
#| msgid "Create Job"
msgid "Create Page"
msgstr "Créer un Emploi"

#: templates/careers_page.html:67
msgid "Workloupe Platform"
msgstr ""

#: templates/careers_page.html:68
msgid "Use our platform as your company's career page"
msgstr ""

#: templates/careers_page.html:70
msgid "Hosted"
msgstr ""

#: templates/careers_page.html:71
msgid "Full Featured"
msgstr ""

#: templates/careers_page.html:76
msgid "Setup Platform"
msgstr ""

#: templates/careers_page.html:91
msgid "Your RSS Feed URL"
msgstr ""

#: templates/careers_page.html:98
msgid ""
"Use this RSS feed URL to automatically sync your job listings with your "
"existing careers page."
msgstr ""

#: templates/careers_page.html:101
msgid "RSS Feed URL"
msgstr ""

#: templates/careers_page.html:106 templates/published_job_details.html:516
#: templates/published_job_details.html:559
#: templates/published_job_details.html:585
#: templates/workloupe_platform.html:311
msgid "Copy"
msgstr ""

#: templates/careers_page.html:111 templates/create_careers_widget.html:182
msgid "Integration Instructions:"
msgstr ""

#: templates/careers_page.html:113
msgid "Copy the RSS feed URL above"
msgstr ""

#: templates/careers_page.html:114
msgid "Add it to your website's RSS feed reader or job board integration"
msgstr ""

#: templates/careers_page.html:115
msgid "Your job listings will automatically sync"
msgstr ""

#: templates/careers_page.html:286 templates/create_careers_widget.html:716
#: templates/wordpress_integration.html:775
#: templates/workloupe_platform.html:1142
msgid "Copied!"
msgstr ""

#: templates/careers_page.html:297 templates/create_careers_widget.html:726
#: templates/wordpress_integration.html:785
msgid "Failed to copy. Please copy manually."
msgstr ""

#: templates/create_careers_widget.html:13
msgid "Widget Builder"
msgstr ""

#: templates/create_careers_widget.html:14
msgid "Customize your careers widget"
msgstr ""

#: templates/create_careers_widget.html:22
#: templates/wordpress_integration.html:45
msgid "Company Branding"
msgstr ""

#: templates/create_careers_widget.html:26
#: templates/wordpress_integration.html:49 templates/workloupe_platform.html:34
msgid "Company Name"
msgstr ""

#: templates/create_careers_widget.html:27
#: templates/wordpress_integration.html:50
msgid "Enter company name"
msgstr ""

#: templates/create_careers_widget.html:31
#: templates/wordpress_integration.html:54
msgid "Tagline"
msgstr ""

#: templates/create_careers_widget.html:32
#: templates/wordpress_integration.html:55
msgid "Enter company tagline"
msgstr ""

#: templates/create_careers_widget.html:36
#: templates/workloupe_platform.html:175
msgid "Company Logo"
msgstr ""

#: templates/create_careers_widget.html:38
msgid "Recommended: 200x80px, PNG or JPG"
msgstr ""

#: templates/create_careers_widget.html:46
msgid "Design & Colors"
msgstr ""

#: templates/create_careers_widget.html:50
#: templates/wordpress_integration.html:67
msgid "Primary Color"
msgstr ""

#: templates/create_careers_widget.html:55
msgid "Background Color"
msgstr ""

#: templates/create_careers_widget.html:60
msgid "Text Color"
msgstr ""

#: templates/create_careers_widget.html:65
msgid "Widget Style"
msgstr ""

#: templates/create_careers_widget.html:67
#: templates/wordpress_integration.html:75
msgid "Modern"
msgstr ""

#: templates/create_careers_widget.html:68
#: templates/wordpress_integration.html:76
msgid "Classic"
msgstr ""

#: templates/create_careers_widget.html:69
#: templates/wordpress_integration.html:77
msgid "Minimal"
msgstr ""

#: templates/create_careers_widget.html:78
#: templates/wordpress_integration.html:93
#, fuzzy
#| msgid "Language Settings"
msgid "Content Settings"
msgstr "Paramètres de Langue"

#: templates/create_careers_widget.html:82
msgid "Max Jobs to Display"
msgstr ""

#: templates/create_careers_widget.html:84
#: templates/create_careers_widget.html:85
#: templates/create_careers_widget.html:86 templates/jobs.html:186
#: templates/wordpress_integration.html:99
#: templates/wordpress_integration.html:100
#: templates/wordpress_integration.html:101
msgid "jobs"
msgstr ""

#: templates/create_careers_widget.html:87
#: templates/wordpress_integration.html:102
msgid "All jobs"
msgstr ""

#: templates/create_careers_widget.html:94
msgid "Show Salary Information"
msgstr ""

#: templates/create_careers_widget.html:101
#, fuzzy
#| msgid "Locations"
msgid "Show Job Location"
msgstr "Emplacements"

#: templates/create_careers_widget.html:108
msgid "Show Posted Date"
msgstr ""

#: templates/create_careers_widget.html:117
msgid "Generate Widget Code"
msgstr ""

#: templates/create_careers_widget.html:126
msgid "Live Preview"
msgstr ""

#: templates/create_careers_widget.html:160
msgid "Your Widget Code"
msgstr ""

#: templates/create_careers_widget.html:167
msgid ""
"Copy this code and paste it into your website where you want the careers "
"widget to appear."
msgstr ""

#: templates/create_careers_widget.html:172
msgid "HTML Widget Code"
msgstr ""

#: templates/create_careers_widget.html:175
#: templates/wordpress_integration.html:246
msgid "Copy Code"
msgstr ""

#: templates/create_careers_widget.html:184
msgid "Copy the HTML code above"
msgstr ""

#: templates/create_careers_widget.html:185
msgid "Paste it into your website's HTML where you want the widget to appear"
msgstr ""

#: templates/create_careers_widget.html:186
msgid "The widget will automatically load your latest job postings"
msgstr ""

#: templates/create_careers_widget.html:187
msgid "The widget is responsive and will adapt to your website's layout"
msgstr ""

#: templates/create_careers_widget.html:192
#: templates/wordpress_integration.html:253
#: templates/workloupe_platform.html:338
msgid "Close"
msgstr ""

#: templates/create_careers_widget.html:195
msgid "Download as HTML File"
msgstr ""

#: templates/create_careers_widget.html:802
msgid ""
"Widget package downloaded! Extract and follow the README instructions for "
"integration."
msgstr ""

#: templates/create_careers_widget.html:823
msgid "Widget file downloaded! Copy the code and paste it into your website."
msgstr ""

#: templates/create_job.html:3
#, fuzzy
#| msgid "Create Job"
msgid "Create Job Position"
msgstr "Créer un Emploi"

#: templates/create_job.html:8
msgid "Basic Information"
msgstr ""

#: templates/create_job.html:10 templates/job_details.html:1072
#: templates/job_preview_publish.html:590
msgid "Role Title"
msgstr ""

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr ""

#: templates/create_job.html:18 templates/job_details.html:1076
#: templates/job_preview_publish.html:594
#, fuzzy
#| msgid "Locations"
msgid "Office Location"
msgstr "Emplacements"

#: templates/create_job.html:21
msgid "Select office location"
msgstr ""

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr ""

#: templates/create_job.html:32
msgid "add office locations"
msgstr ""

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74
msgid "in your preferences first."
msgstr ""

#: templates/create_job.html:36
msgid "No locations available"
msgstr ""

#: templates/create_job.html:41 templates/job_details.html:1080
#: templates/job_preview_publish.html:598
#, fuzzy
#| msgid "Work Schedules"
msgid "Work Schedule"
msgstr "Horaires de Travail"

#: templates/create_job.html:44 templates/create_job.html:65
msgid "Select an option"
msgstr ""

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr ""

#: templates/create_job.html:53
#, fuzzy
#| msgid "Work Schedules"
msgid "add work schedules"
msgstr "Horaires de Travail"

#: templates/create_job.html:57
msgid "No work schedules available"
msgstr ""

#: templates/create_job.html:62 templates/job_details.html:1084
#: templates/job_preview_publish.html:604
#, fuzzy
#| msgid "Office Schedules"
msgid "Office Schedule"
msgstr "Horaires de Bureau"

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr ""

#: templates/create_job.html:74
#, fuzzy
#| msgid "Office Schedules"
msgid "add office schedules"
msgstr "Horaires de Bureau"

#: templates/create_job.html:78
#, fuzzy
#| msgid "Office Schedules"
msgid "No office schedules available"
msgstr "Horaires de Bureau"

#: templates/create_job.html:86
msgid "Skills Requirements"
msgstr ""

#: templates/create_job.html:88
msgid "Skill"
msgstr ""

#: templates/create_job.html:93
msgid "e.g. JavaScript"
msgstr ""

#: templates/create_job.html:95 templates/create_job.html:169
msgid "Add"
msgstr ""

#: templates/create_job.html:98
msgid "Choose Skills"
msgstr ""

#: templates/create_job.html:112
msgid "Selected Skills"
msgstr ""

#: templates/create_job.html:115
msgid "No skills selected yet"
msgstr ""

#: templates/create_job.html:125
msgid "Salary Details (Optional)"
msgstr ""

#: templates/create_job.html:128
msgid "Minimum Salary"
msgstr ""

#: templates/create_job.html:132
msgid "Enter minimum salary"
msgstr ""

#: templates/create_job.html:136
msgid "Maximum Salary"
msgstr ""

#: templates/create_job.html:140
msgid "Enter maximum salary"
msgstr ""

#: templates/create_job.html:144
#, fuzzy
#| msgid "Current"
msgid "Currency"
msgstr "Actuel"

#: templates/create_job.html:146
msgid "Select currency"
msgstr ""

#: templates/create_job.html:162
msgid "Benefits and Highlights (Optional)"
msgstr ""

#: templates/create_job.html:167
msgid "e.g. Yearly Bonuses"
msgstr ""

#: templates/create_job.html:172
msgid "Choose Benefits"
msgstr ""

#: templates/create_job.html:175
msgid "Dental Coverage"
msgstr ""

#: templates/create_job.html:178
msgid "Private Health Coverage"
msgstr ""

#: templates/create_job.html:181
msgid "Gym membership"
msgstr ""

#: templates/create_job.html:184
msgid "Sign-in Bonus"
msgstr ""

#: templates/create_job.html:187
msgid "Relocation Package"
msgstr ""

#: templates/create_job.html:190
msgid "Company Vehicle"
msgstr ""

#: templates/create_job.html:192
msgid "Food Card"
msgstr ""

#: templates/create_job.html:194
msgid "Snacks & Coffee"
msgstr ""

#: templates/create_job.html:197
msgid "Pet Friendly Office"
msgstr ""

#: templates/create_job.html:201
msgid "Selected Benefits & Highlights"
msgstr ""

#: templates/create_job.html:204
msgid "No benefits or highlights selected yet"
msgstr ""

#: templates/create_job.html:211 templates/job_details.html:107
#: templates/job_preview_publish.html:159
msgid "Discard"
msgstr ""

#: templates/create_job.html:212
msgid "Next"
msgstr ""

#: templates/create_job_template.html:13 templates/create_job_template.html:19
#: templates/settings.html:41
msgid "Templates"
msgstr ""

#: templates/create_job_template.html:14
#, fuzzy
#| msgid "Configure standard options to streamline your job creation process"
msgid "Create and manage reusable job description templates"
msgstr ""
"Configurez les options standard pour rationaliser votre processus de "
"création d'emplois"

#: templates/create_job_template.html:17 templates/job_preferences.html:15
#: templates/manage_permissions.html:15 templates/navbar.html:104
#: templates/settings.html:9
msgid "Settings"
msgstr "Paramètres"

#: templates/create_job_template.html:31
#, fuzzy
#| msgid "Applicants"
msgid "Total Templates"
msgstr "Candidats"

#: templates/create_job_template.html:41
#, fuzzy
#| msgid "Month"
msgid "Created This Month"
msgstr "Mois"

#: templates/create_job_template.html:51
msgid "Jobs Created from Templates"
msgstr ""

#: templates/create_job_template.html:61
msgid "Time Saved Using Templates"
msgstr ""

#: templates/create_job_template.html:71
msgid "My Templates"
msgstr ""

#: templates/create_job_template.html:74 templates/create_job_template.html:105
#: templates/create_job_template.html:118
msgid "New Template"
msgstr ""

#: templates/create_job_template.html:80
#, fuzzy
#| msgid "Search applicants..."
msgid "Search templates..."
msgstr "Rechercher des candidats..."

#: templates/create_job_template.html:91
msgid "Updated"
msgstr ""

#: templates/create_job_template.html:94
msgid "Used 1 time"
msgstr ""

#: templates/create_job_template.html:96
msgid "Used"
msgstr ""

#: templates/create_job_template.html:96
msgid "times"
msgstr ""

#: templates/create_job_template.html:107
msgid "Not saved yet"
msgstr ""

#: templates/create_job_template.html:108
msgid "Not used yet"
msgstr ""

#: templates/create_job_template.html:118
msgid "Enter template title"
msgstr ""

#: templates/create_job_template.html:121
#: templates/create_job_template.html:196
msgid "Delete Template"
msgstr ""

#: templates/create_job_template.html:126 templates/job_details.html:135
msgid "Save Template"
msgstr ""

#: templates/create_job_template.html:136
msgid "Heading 1"
msgstr ""

#: templates/create_job_template.html:137
msgid "Heading 2"
msgstr ""

#: templates/create_job_template.html:138
msgid "Heading 3"
msgstr ""

#: templates/create_job_template.html:139
msgid "Paragraph"
msgstr ""

#: templates/create_job_template.html:143
msgid "Bold"
msgstr ""

#: templates/create_job_template.html:144
msgid "Italic"
msgstr ""

#: templates/create_job_template.html:145
msgid "Underline"
msgstr ""

#: templates/create_job_template.html:149
msgid "Bullet List"
msgstr ""

#: templates/create_job_template.html:150
msgid "Numbered List"
msgstr ""

#: templates/create_job_template.html:179
msgid "Enter your template content here..."
msgstr ""

#: templates/create_job_template.html:185 templates/job_details.html:100
msgid "characters"
msgstr ""

#: templates/create_job_template.html:201
msgid "Are you sure you want to delete the"
msgstr ""

#: templates/create_job_template.html:201
msgid "template? This action cannot be undone."
msgstr ""

#: templates/create_job_template.html:206 templates/job_preferences.html:70
msgid "Delete"
msgstr ""

#: templates/feed.html:20 templates/feed.html:46
msgid "Loading..."
msgstr ""

#: templates/feed.html:32
msgid "Calendar"
msgstr "Calendrier"

#: templates/feed.html:34
msgid "Day"
msgstr "Jour"

#: templates/feed.html:35
msgid "Week"
msgstr "Semaine"

#: templates/feed.html:36
msgid "Month"
msgstr "Mois"

#: templates/feed.html:51 templates/jobs.html:92 templates/people.html:64
msgid "Today"
msgstr "Aujourd'hui"

#: templates/feed.html:56
msgid "Click on a day with colored dots to view events"
msgstr ""

#: templates/feed.html:61 templates/feed.html:74
#, fuzzy
#| msgid "Month"
msgid "Mon"
msgstr "Mois"

#: templates/feed.html:62 templates/feed.html:75
msgid "Tue"
msgstr ""

#: templates/feed.html:63 templates/feed.html:76
msgid "Wed"
msgstr ""

#: templates/feed.html:64 templates/feed.html:77
msgid "Thu"
msgstr ""

#: templates/feed.html:65 templates/feed.html:78
msgid "Fri"
msgstr ""

#: templates/feed.html:66 templates/feed.html:79
msgid "Sat"
msgstr ""

#: templates/feed.html:67 templates/feed.html:80
msgid "Sun"
msgstr ""

#: templates/feed.html:96
msgid "Activity Feed"
msgstr "Flux d'Activité"

#: templates/feed.html:100
msgid "Clear"
msgstr ""

#: templates/feed.html:147 templates/feed.html:3030
#, fuzzy
#| msgid "Profile"
msgid "No Recent Activity"
msgstr "Profil"

#: templates/feed.html:149 templates/feed.html:3032
msgid ""
"Activity will appear here when candidates apply, change status, or when you "
"post new jobs."
msgstr ""

#: templates/feed.html:166
msgid "Hot"
msgstr ""

#: templates/feed.html:166 templates/navbar.html:36
msgid "Jobs"
msgstr "Emplois"

#: templates/feed.html:169
msgid "View All Jobs"
msgstr ""

#: templates/feed.html:186 templates/feed.html:245 templates/jobs.html:129
#: templates/navbar.html:42
msgid "Applicants"
msgstr "Candidats"

#: templates/feed.html:203
msgid "No Hot Jobs Yet"
msgstr ""

#: templates/feed.html:205
msgid ""
"Create your first job posting to start attracting candidates and see "
"trending positions here."
msgstr ""

#: templates/feed.html:209 templates/navbar.html:57
msgid "Create Job"
msgstr "Créer un Emploi"

#: templates/feed.html:212 templates/jobs.html:175
#, fuzzy
#| msgid "Applicants"
msgid "Browse Templates"
msgstr "Candidats"

#: templates/feed.html:228
msgid "Monthly Applicant Overview"
msgstr ""

#: templates/feed.html:267
msgid "Events for Date"
msgstr ""

#: templates/feed.html:289
msgid "Add New Event"
msgstr ""

#: templates/feed.html:299
msgid "Create New Event"
msgstr ""

#: templates/feed.html:306
msgid "Enter event title"
msgstr ""

#: templates/feed.html:393
msgid "Select the relevant position"
msgstr ""

#: templates/feed.html:400
msgid "No vacancies available"
msgstr ""

#: templates/feed.html:413
msgid "Pick a Vacancy to see candidates"
msgstr ""

#: templates/feed.html:436
msgid "Enter meeting link"
msgstr ""

#: templates/feed.html:2975
msgid ""
"Are you sure you want to clear all activity notifications? This action "
"cannot be undone."
msgstr ""

#: templates/feed.html:3046
msgid "Activity feed cleared successfully"
msgstr ""

#: templates/feed.html:3048
msgid "Failed to clear activity feed"
msgstr ""

#: templates/feed.html:3053
msgid "An error occurred while clearing activity feed"
msgstr ""

#: templates/job_details.html:7 templates/job_details.html:20
#: templates/job_details.html:51 templates/job_preview_publish.html:28
msgid "Job Description"
msgstr ""

#: templates/job_details.html:11 templates/job_preview_publish.html:17
msgid "Job Summary"
msgstr ""

#: templates/job_details.html:14 templates/job_preview_publish.html:21
msgid "Loading job details..."
msgstr ""

#: templates/job_details.html:24
#, fuzzy
#| msgid "Create Job"
msgid "Create new description"
msgstr "Créer un Emploi"

#: templates/job_details.html:28
msgid "Use saved template"
msgstr ""

#: templates/job_details.html:33
#, fuzzy
#| msgid "Applicants"
msgid "Choose a template:"
msgstr "Candidats"

#: templates/job_details.html:35
#, fuzzy
#| msgid "Search applicants..."
msgid "Select a template"
msgstr "Rechercher des candidats..."

#: templates/job_details.html:42
msgid "AI Job Description Generator"
msgstr ""

#: templates/job_details.html:43
msgid ""
"Let AI create a professional job description based on your job details above."
msgstr ""

#: templates/job_details.html:46
msgid "Generate with AI"
msgstr ""

#: templates/job_details.html:52
msgid ""
"Describe the position, responsibilities, qualifications, and any other "
"relevant details."
msgstr ""

#: templates/job_details.html:106 templates/job_preview_publish.html:158
msgid "Back"
msgstr ""

#: templates/job_details.html:111
#, fuzzy
#| msgid "Applicants"
msgid "Update Template"
msgstr "Candidats"

#: templates/job_details.html:114 templates/job_details.html:124
#, fuzzy
#| msgid "Applicants"
msgid "Save as Template"
msgstr "Candidats"

#: templates/job_details.html:117
msgid "Save & Continue"
msgstr ""

#: templates/job_details.html:129
msgid "Template Title"
msgstr ""

#: templates/job_details.html:130
msgid "Enter a name for this template"
msgstr ""

#: templates/job_details.html:1060
msgid ""
"No job information found. Please go back and fill out the job details form."
msgstr ""

#: templates/job_details.html:1089 templates/job_preview_publish.html:610
msgid "Salary Details"
msgstr ""

#: templates/job_details.html:1094 templates/job_preview_publish.html:617
msgid "Benefits & Highlights"
msgstr ""

#: templates/job_details.html:1104 templates/job_preview_publish.html:633
msgid "Skills"
msgstr ""

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr "Préférences"

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr ""
"Configurez les options standard pour rationaliser votre processus de "
"création d'emplois"

#: templates/job_preferences.html:26 templates/job_preferences.html:51
msgid "Work Schedules"
msgstr "Horaires de Travail"

#: templates/job_preferences.html:30 templates/job_preferences.html:84
msgid "Office Schedules"
msgstr "Horaires de Bureau"

#: templates/job_preferences.html:34
msgid "Locations"
msgstr "Emplacements"

#: templates/job_preferences.html:38
msgid "Departments"
msgstr "Départements"

#: templates/job_preferences.html:42
msgid "Language"
msgstr "Langue"

#: templates/job_preferences.html:52
msgid "Define standard work schedule types for your organization"
msgstr ""

#: templates/job_preferences.html:56
#, fuzzy
#| msgid "Work Schedules"
msgid "Add Work Schedule"
msgstr "Horaires de Travail"

#: templates/job_preferences.html:64
#, fuzzy
#| msgid "Work Schedules"
msgid "Search work schedules..."
msgstr "Horaires de Travail"

#: templates/job_preferences.html:67
msgid "Select All"
msgstr ""

#: templates/job_preferences.html:85
msgid "Define where and how employees work"
msgstr ""

#: templates/job_preferences.html:89
#, fuzzy
#| msgid "Office Schedules"
msgid "Add Office Schedule"
msgstr "Horaires de Bureau"

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr "Paramètres de Langue"

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr "Choisissez votre langue préférée pour l'interface de l'application"

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr "Langue de l'Interface"

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr ""
"Sélectionnez la langue que vous souhaitez utiliser pour l'interface de "
"l'application"

#: templates/job_preferences.html:210
msgid "Current"
msgstr "Actuel"

#: templates/job_preferences.html:223 templates/published_job_details.html:652
#: templates/workloupe_platform.html:213
msgid "Note:"
msgstr "Note :"

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr ""
"Changer la langue actualisera la page pour appliquer les nouveaux paramètres "
"de langue."

#: templates/job_preview_publish.html:7 templates/job_preview_publish.html:162
msgid "Publish Job"
msgstr ""

#: templates/job_preview_publish.html:11
msgid "Final Review"
msgstr ""

#: templates/job_preview_publish.html:12
msgid "Please review the job details before publishing."
msgstr ""

#: templates/job_preview_publish.html:32
msgid "Loading job description..."
msgstr ""

#: templates/job_preview_publish.html:39
msgid "Publish To"
msgstr ""

#: templates/job_preview_publish.html:41
msgid ""
"Select the job portals where you want to publish this job posting. <br> <br> "
"<i> if the portal you want to publish to is grayed out, it means that you "
"have not yet adjusted the related configuration settings. </i>"
msgstr ""

#: templates/job_preview_publish.html:61
msgid ""
"Professional networking platform with over 750 million users worldwide. "
"Selecting this option will open a new tab for you to complete the job "
"posting."
msgstr ""

#: templates/job_preview_publish.html:82
msgid ""
"Job and company review site focusing on workplace transparency. Selecting "
"this option will open a new tab for you to complete the job posting."
msgstr ""

#: templates/job_preview_publish.html:103
#, python-format
msgid ""
"Specialized job platform for tech and creative professionals powered by "
"Workloupe. Workloupe is 100%% free to use."
msgstr ""

#: templates/job_preview_publish.html:125
msgid ""
"One of the biggest remote job focused job platforms in the world. Posting to "
"Himalayas is free. "
msgstr ""

#: templates/job_preview_publish.html:147
msgid ""
"PostJobFree has more than 7 million jobs, and it's free to post to. Their "
"job portal is focused on simplicity and ease of use."
msgstr ""

#: templates/job_preview_publish.html:580
msgid ""
"No job information found. Please go back and fill out the job details form. "
msgstr ""

#: templates/job_preview_publish.html:658
msgid "No job description found. Please go back and create a job description."
msgstr ""

#: templates/jobs.html:8
msgid "Job Listings"
msgstr ""

#: templates/jobs.html:18
#, fuzzy
#| msgid "Activity Feed"
msgid "Active Jobs"
msgstr "Flux d'Activité"

#: templates/jobs.html:28
#, fuzzy
#| msgid "Applicants"
msgid "Total Applicants"
msgstr "Candidats"

#: templates/jobs.html:38
msgid "Archived Jobs"
msgstr ""

#: templates/jobs.html:48
#, fuzzy
#| msgid "Jobs"
msgid "On-Hold Jobs"
msgstr "Emplois"

#: templates/jobs.html:59 templates/profile.html:119
#, fuzzy
#| msgid "Departments"
msgid "Department"
msgstr "Départements"

#: templates/jobs.html:61
#, fuzzy
#| msgid "Departments"
msgid "All Departments"
msgstr "Départements"

#: templates/jobs.html:71 templates/manage_permissions.html:212
#: templates/people.html:43
msgid "All Statuses"
msgstr ""

#: templates/jobs.html:79 templates/people.html:51 templates/people.html:90
#, fuzzy
#| msgid "Locations"
msgid "Location"
msgstr "Emplacements"

#: templates/jobs.html:81 templates/people.html:53
#, fuzzy
#| msgid "Locations"
msgid "All Locations"
msgstr "Emplacements"

#: templates/jobs.html:89
msgid "Posted Date"
msgstr ""

#: templates/jobs.html:91
msgid "All Time"
msgstr ""

#: templates/jobs.html:93 templates/people.html:65
#, fuzzy
#| msgid "Week"
msgid "This Week"
msgstr "Semaine"

#: templates/jobs.html:94 templates/people.html:66
#, fuzzy
#| msgid "Month"
msgid "This Month"
msgstr "Mois"

#: templates/jobs.html:95
#, fuzzy
#| msgid "Month"
msgid "Last Month"
msgstr "Mois"

#: templates/jobs.html:106 templates/people.html:76
msgid "Clear all filters"
msgstr ""

#: templates/jobs.html:135
msgid "Interviews"
msgstr ""

#: templates/jobs.html:146
msgid "Days Open"
msgstr ""

#: templates/jobs.html:152
msgid "Closed on:"
msgstr ""

#: templates/jobs.html:152
msgid "Posted on:"
msgstr ""

#: templates/jobs.html:153
msgid "View Details"
msgstr ""

#: templates/jobs.html:166
msgid "No Job Postings Yet"
msgstr ""

#: templates/jobs.html:168
msgid ""
"You haven't published any job postings yet. Create your first job posting to "
"start attracting candidates."
msgstr ""

#: templates/jobs.html:172
#, fuzzy
#| msgid "Create Job"
msgid "Create Your First Job"
msgstr "Créer un Emploi"

#: templates/jobs.html:186 templates/people.html:174
msgid "Showing"
msgstr ""

#: templates/jobs.html:186 templates/people.html:174
msgid "of"
msgstr ""

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr ""

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr ""

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/manage_permissions.html:73 templates/settings.html:62
#, fuzzy
#| msgid "Locations"
msgid "Invitations"
msgstr "Emplacements"

#: templates/manage_permissions.html:29 templates/manage_permissions.html:69
msgid "Team Members"
msgstr ""

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr ""

#: templates/manage_permissions.html:84
#, fuzzy
#| msgid "Search applicants..."
msgid "Search team members..."
msgstr "Rechercher des candidats..."

#: templates/manage_permissions.html:88 templates/manage_permissions.html:218
msgid "Role:"
msgstr ""

#: templates/manage_permissions.html:90 templates/manage_permissions.html:220
msgid "All Roles"
msgstr ""

#: templates/manage_permissions.html:105 templates/manage_permissions.html:244
#: templates/people.html:87 templates/profile.html:49
#: templates/published_job_details.html:276
msgid "Name"
msgstr ""

#: templates/manage_permissions.html:109 templates/manage_permissions.html:248
#: templates/profile.html:57 templates/profile.html:101
#: templates/register.html:24
msgid "Email"
msgstr ""

#: templates/manage_permissions.html:113 templates/manage_permissions.html:252
#: templates/manage_permissions.html:381
msgid "Role"
msgstr ""

#: templates/manage_permissions.html:120 templates/manage_permissions.html:267
#: templates/profile.html:80 templates/published_job_details.html:285
#, fuzzy
#| msgid "Activity Feed"
msgid "Actions"
msgstr "Flux d'Activité"

#: templates/manage_permissions.html:143
#, fuzzy
#| msgid "Activity Feed"
msgid "Deactivate"
msgstr "Flux d'Activité"

#: templates/manage_permissions.html:147
#, fuzzy
#| msgid "Activity Feed"
msgid "Activate"
msgstr "Flux d'Activité"

#: templates/manage_permissions.html:206
#, fuzzy
#| msgid "Search applicants..."
msgid "Search invitations..."
msgstr "Rechercher des candidats..."

#: templates/manage_permissions.html:210
#, fuzzy
#| msgid "Search applicants..."
msgid "Status:"
msgstr "Rechercher des candidats..."

#: templates/manage_permissions.html:231
msgid "Invite New Member"
msgstr ""

#: templates/manage_permissions.html:256
msgid "Sent Date"
msgstr ""

#: templates/manage_permissions.html:260
msgid "Expiry Date"
msgstr ""

#: templates/manage_permissions.html:305
#, fuzzy
#| msgid "Locations"
msgid "No invitations found"
msgstr "Emplacements"

#: templates/manage_permissions.html:353
msgid "Invite New Team Member"
msgstr ""

#: templates/manage_permissions.html:360
msgid "Recipient Information"
msgstr ""

#: templates/manage_permissions.html:364 templates/profile.html:93
msgid "First Name"
msgstr ""

#: templates/manage_permissions.html:365
msgid "Enter first name"
msgstr ""

#: templates/manage_permissions.html:369 templates/profile.html:97
msgid "Last Name"
msgstr ""

#: templates/manage_permissions.html:370
msgid "Enter last name"
msgstr ""

#: templates/manage_permissions.html:375 templates/signin.html:17
msgid "Email Address"
msgstr ""

#: templates/manage_permissions.html:376
msgid "Enter email address"
msgstr ""

#: templates/manage_permissions.html:383 templates/manage_permissions.html:443
#, fuzzy
#| msgid "Search applicants..."
msgid "Select a role"
msgstr "Rechercher des candidats..."

#: templates/manage_permissions.html:384 templates/manage_permissions.html:444
msgid "Administrator"
msgstr ""

#: templates/manage_permissions.html:385 templates/manage_permissions.html:437
#: templates/manage_permissions.html:445
msgid "Recruiter"
msgstr ""

#: templates/manage_permissions.html:386 templates/manage_permissions.html:446
msgid "Hiring Manager"
msgstr ""

#: templates/manage_permissions.html:387 templates/manage_permissions.html:447
msgid "Interviewer"
msgstr ""

#: templates/manage_permissions.html:388 templates/manage_permissions.html:448
msgid "Read Only"
msgstr ""

#: templates/manage_permissions.html:395
msgid "Permissions"
msgstr ""

#: templates/manage_permissions.html:396
msgid ""
"Permissions are determined by the selected role. You can customize them "
"after the user has accepted the invitation."
msgstr ""

#: templates/manage_permissions.html:400
#, fuzzy
#| msgid "Create Job"
msgid "Role Descriptions:"
msgstr "Créer un Emploi"

#: templates/manage_permissions.html:402
msgid "Administrator: Full access to all system features and settings."
msgstr ""

#: templates/manage_permissions.html:403
msgid "Recruiter: Manage job postings, candidates, and interviews."
msgstr ""

#: templates/manage_permissions.html:404
msgid "Hiring Manager: Review candidates and make hiring decisions."
msgstr ""

#: templates/manage_permissions.html:405
msgid "Interviewer: Conduct interviews and provide feedback."
msgstr ""

#: templates/manage_permissions.html:406
msgid "Read Only: View-only access to recruitment data."
msgstr ""

#: templates/manage_permissions.html:414
#, fuzzy
#| msgid "Locations"
msgid "Send Invitation"
msgstr "Emplacements"

#: templates/manage_permissions.html:425 templates/manage_permissions.html:464
#, fuzzy
#| msgid "Create Job"
msgid "Change Role"
msgstr "Créer un Emploi"

#: templates/manage_permissions.html:431
msgid "Team Member"
msgstr ""

#: templates/manage_permissions.html:436
#, fuzzy
#| msgid "Current"
msgid "Current Role"
msgstr "Actuel"

#: templates/manage_permissions.html:441
msgid "New Role*"
msgstr ""

#: templates/manage_permissions.html:453
msgid "Reason for Change (Optional)"
msgstr ""

#: templates/manage_permissions.html:454
msgid "Provide a reason for this role change"
msgstr ""

#: templates/manage_permissions.html:459
msgid ""
"Changing roles will update the user's permissions. They will be notified of "
"this change."
msgstr ""

#: templates/navbar.html:31
msgid "Feed"
msgstr "Flux"

#: templates/navbar.html:94
msgid "Employee"
msgstr ""

#: templates/navbar.html:100
msgid "Profile"
msgstr "Profil"

#: templates/navbar.html:109
msgid "Logout"
msgstr "Déconnexion"

#: templates/navbar.html:118
msgid "Guest User"
msgstr ""

#: templates/navbar.html:121
msgid "Not logged in"
msgstr ""

#: templates/navbar.html:126 templates/signin.html:97
msgid "Sign In"
msgstr ""

#: templates/people.html:10
msgid "Applicant Tracking"
msgstr "Suivi des Candidats"

#: templates/people.html:14 templates/people.html:1034
#, fuzzy
#| msgid "Applicants"
msgid "Refresh Applicants"
msgstr "Candidats"

#: templates/people.html:18
msgid "Search applicants..."
msgstr "Rechercher des candidats..."

#: templates/people.html:33
msgid "All Positions"
msgstr ""

#: templates/people.html:63
msgid "All Dates"
msgstr ""

#: templates/people.html:91
msgid "Experience (Years)"
msgstr ""

#: templates/people.html:92 templates/published_job_details.html:282
msgid "Score"
msgstr ""

#: templates/people.html:93
#, fuzzy
#| msgid "Applicants"
msgid "Applied On"
msgstr "Candidats"

#: templates/people.html:94
#, fuzzy
#| msgid "Activity Feed"
msgid "Action"
msgstr "Flux d'Activité"

#: templates/people.html:139 templates/published_job_details.html:343
msgid "View"
msgstr ""

#: templates/people.html:154 templates/published_job_details.html:246
#, fuzzy
#| msgid "Applicants"
msgid "No Applicants Yet"
msgstr "Candidats"

#: templates/people.html:156
msgid ""
"Nobody has applied to your job postings yet. Once candidates start applying, "
"you'll see them here."
msgstr ""

#: templates/people.html:160
#, fuzzy
#| msgid "Create Job"
msgid "View Job Postings"
msgstr "Créer un Emploi"

#: templates/people.html:163
#, fuzzy
#| msgid "Create Job"
msgid "Create New Job"
msgstr "Créer un Emploi"

#: templates/people.html:174
#, fuzzy
#| msgid "Applicants"
msgid "applicants"
msgstr "Candidats"

#: templates/people.html:227
msgid "Show"
msgstr ""

#: templates/people.html:234
msgid "per page"
msgstr ""

#: templates/people.html:969
msgid "Processing..."
msgstr ""

#: templates/people.html:1015
msgid "Success!"
msgstr ""

#: templates/people.html:1022 templates/workloupe_platform.html:1119
msgid "Error:"
msgstr ""

#: templates/people.html:1027
msgid "An error occurred while processing applications. Please try again."
msgstr ""

#: templates/profile.html:10
#, fuzzy
#| msgid "Profile"
msgid "Your Profile"
msgstr "Profil"

#: templates/profile.html:29
msgid "Change Photo"
msgstr ""

#: templates/profile.html:32
#, fuzzy
#| msgid "Profile"
msgid "Profile Activity"
msgstr "Profil"

#: templates/profile.html:33
#, fuzzy
#| msgid "Month"
msgid "Last Login:"
msgstr "Mois"

#: templates/profile.html:36
msgid "Account Created:"
msgstr ""

#: templates/profile.html:46 templates/profile.html:91
msgid "Personal Information"
msgstr ""

#: templates/profile.html:63 templates/profile.html:109
#: templates/workloupe_platform.html:28
msgid "Company Information"
msgstr ""

#: templates/profile.html:66 templates/profile.html:111
msgid "Company"
msgstr ""

#: templates/profile.html:83 templates/profile.html:163
#: templates/profile.html:185
#, fuzzy
#| msgid "Create Job"
msgid "Change Password"
msgstr "Créer un Emploi"

#: templates/profile.html:105
msgid "Phone"
msgstr ""

#: templates/profile.html:124
msgid "Save Changes"
msgstr ""

#: templates/profile.html:139
msgid "Upload Profile Photo"
msgstr ""

#: templates/profile.html:146
msgid "Choose a photo (PNG or JPEG only)"
msgstr ""

#: templates/profile.html:153
msgid "Upload"
msgstr ""

#: templates/profile.html:170
#, fuzzy
#| msgid "Create Job"
msgid "Current Password"
msgstr "Créer un Emploi"

#: templates/profile.html:174
#, fuzzy
#| msgid "Create Job"
msgid "New Password"
msgstr "Créer un Emploi"

#: templates/profile.html:178
#, fuzzy
#| msgid "Create Job"
msgid "Confirm New Password"
msgstr "Créer un Emploi"

#: templates/published_job_details.html:58
#, fuzzy
#| msgid "Locations"
msgid "Notification"
msgstr "Emplacements"

#: templates/published_job_details.html:82
#, fuzzy
#| msgid "Applicants"
msgid "Total Applicants:"
msgstr "Candidats"

#: templates/published_job_details.html:87
msgid "Published At:"
msgstr ""

#: templates/published_job_details.html:111
msgid "Bulk Communication"
msgstr ""

#: templates/published_job_details.html:124
msgid "Expert Support Options"
msgstr ""

#: templates/published_job_details.html:136
msgid "Post on LinkedIn"
msgstr ""

#: templates/published_job_details.html:148
#: templates/published_job_details.html:641
msgid "Change Vacancy Status"
msgstr ""

#: templates/published_job_details.html:162
#, fuzzy
#| msgid "Applicants"
msgid "Applicants Over Time"
msgstr "Candidats"

#: templates/published_job_details.html:170
#, fuzzy
#| msgid "Applicants"
msgid "No Application Data Yet"
msgstr "Candidats"

#: templates/published_job_details.html:171
msgid ""
"Once candidates start applying, you'll see application trends over time here."
msgstr ""

#: templates/published_job_details.html:184
#, fuzzy
#| msgid "Applicants"
msgid "Number of Applicants by Job Portal"
msgstr "Candidats"

#: templates/published_job_details.html:191
msgid "No Portal Data Yet"
msgstr ""

#: templates/published_job_details.html:192
msgid ""
"When applications come in, you'll see which job portals are most effective "
"here."
msgstr ""

#: templates/published_job_details.html:202
msgid "Distribution of Applicants by Status"
msgstr ""

#: templates/published_job_details.html:209
msgid "No Status Data Yet"
msgstr ""

#: templates/published_job_details.html:210
msgid ""
"Application status distribution will appear here as you review candidates."
msgstr ""

#: templates/published_job_details.html:225
#, fuzzy
#| msgid "Applicants"
msgid "Top Applicants"
msgstr "Candidats"

#: templates/published_job_details.html:233
#, fuzzy
#| msgid "Applicants"
msgid "View All Applicants"
msgstr "Candidats"

#: templates/published_job_details.html:249
msgid ""
"Don't worry! Once candidates start applying to this position, they will "
"appear here. You can track their progress, review their profiles, and manage "
"the hiring process."
msgstr ""

#: templates/published_job_details.html:256
msgid "What happens next?"
msgstr ""

#: templates/published_job_details.html:259
msgid "Candidates will apply through your job posting"
msgstr ""

#: templates/published_job_details.html:260
msgid "You'll see their profiles and CVs here"
msgstr ""

#: templates/published_job_details.html:261
msgid "You can review, rate, and manage applications"
msgstr ""

#: templates/published_job_details.html:262
msgid "Use the communication tools to contact candidates"
msgstr ""

#: templates/published_job_details.html:332
msgid "Not Rated"
msgstr ""

#: templates/published_job_details.html:360
msgid "Request Support From Experts"
msgstr ""

#: templates/published_job_details.html:365
msgid ""
"We can provide vetted candidates from our talent pool, or help you during "
"the technical interviews to pick best fit for your expectations."
msgstr ""

#: templates/published_job_details.html:370
msgid "Enter Details"
msgstr ""

#: templates/published_job_details.html:381
msgid "Request Candidates"
msgstr ""

#: templates/published_job_details.html:382
msgid "Request Interview Help"
msgstr ""

#: templates/published_job_details.html:396
msgid "Send Bulk Mail to Applicants"
msgstr ""

#: templates/published_job_details.html:401
msgid ""
"Use this form to send bulk emails to applicants based on their application "
"statuses."
msgstr ""

#: templates/published_job_details.html:406
#, fuzzy
#| msgid "Applicants"
msgid "Select Application Status"
msgstr "Candidats"

#: templates/published_job_details.html:413
#, fuzzy
#| msgid "Search applicants..."
msgid "Select a status"
msgstr "Rechercher des candidats..."

#: templates/published_job_details.html:420
msgid "Email Subject"
msgstr ""

#: templates/published_job_details.html:439
msgid "Enter internal notes for your team (optional)"
msgstr ""

#: templates/published_job_details.html:443
msgid "(sent to candidates)"
msgstr ""

#: templates/published_job_details.html:450
msgid "Enter your email message"
msgstr ""

#: templates/published_job_details.html:462
msgid "Send notification emails to candidates"
msgstr ""

#: templates/published_job_details.html:466
msgid "Send Emails"
msgstr ""

#: templates/published_job_details.html:482
msgid "Post this job on LinkedIn"
msgstr ""

#: templates/published_job_details.html:489
msgid ""
"Follow these simple steps to post your job on LinkedIn and reach more "
"candidates."
msgstr ""

#: templates/published_job_details.html:496
msgid "Navigate to LinkedIn Job Posting"
msgstr ""

#: templates/published_job_details.html:499
msgid "Go to"
msgstr ""

#: templates/published_job_details.html:499
msgid "LinkedIn Job Posting Page"
msgstr ""

#: templates/published_job_details.html:507
msgid "Copy Job Title"
msgstr ""

#: templates/published_job_details.html:509
msgid "Copy and paste the job title below:"
msgstr ""

#: templates/published_job_details.html:526
#, fuzzy
#| msgid "Language Settings"
msgid "Configure Job Settings"
msgstr "Paramètres de Langue"

#: templates/published_job_details.html:529
msgid "Select 'Use my own description' option and configure job details:"
msgstr ""

#: templates/published_job_details.html:531
msgid "Reference - Your job settings:"
msgstr ""

#: templates/published_job_details.html:533
#, fuzzy
#| msgid "Locations"
msgid "Location:"
msgstr "Emplacements"

#: templates/published_job_details.html:534
#, fuzzy
#| msgid "Work Schedules"
msgid "Work Schedule:"
msgstr "Horaires de Travail"

#: templates/published_job_details.html:535
#, fuzzy
#| msgid "Office Schedules"
msgid "Office Schedule:"
msgstr "Horaires de Bureau"

#: templates/published_job_details.html:545
#, fuzzy
#| msgid "Create Job"
msgid "Copy Job Description"
msgstr "Créer un Emploi"

#: templates/published_job_details.html:548
#, fuzzy
#| msgid "Configure standard options to streamline your job creation process"
msgid "Copy and paste the job description below:"
msgstr ""
"Configurez les options standard pour rationaliser votre processus de "
"création d'emplois"

#: templates/published_job_details.html:551
msgid ""
"Note: Delete any placeholder text in LinkedIn's editor before pasting. You "
"may have to re-apply some of the styling."
msgstr ""

#: templates/published_job_details.html:570
#, fuzzy
#| msgid "Applicants"
msgid "Configure Application Management"
msgstr "Candidats"

#: templates/published_job_details.html:574
msgid "Click 'Continue' button"
msgstr ""

#: templates/published_job_details.html:575
msgid "Find 'Manage applicants' option and click the pencil icon to edit"
msgstr ""

#: templates/published_job_details.html:576
msgid ""
"Change 'Manage Applications' from 'On LinkedIn' to 'On an External Website'"
msgstr ""

#: templates/published_job_details.html:577
msgid "Copy and paste the application URL below:"
msgstr ""

#: templates/published_job_details.html:596
#, fuzzy
#| msgid "Applicants"
msgid "Review Qualifications"
msgstr "Candidats"

#: templates/published_job_details.html:599
msgid "Review and customize the ideal qualifications section:"
msgstr ""

#: templates/published_job_details.html:601
msgid "Reference - Skills from your job:"
msgstr ""

#: templates/published_job_details.html:615
msgid "Finalize and Publish"
msgstr ""

#: templates/published_job_details.html:619
msgid "Confirm your identity using your work email if required"
msgstr ""

#: templates/published_job_details.html:620
msgid "Choose between free or promoted posting (Recommended: Free)"
msgstr ""

#: templates/published_job_details.html:621
msgid "Click 'Post Job' button"
msgstr ""

#: templates/published_job_details.html:622
msgid "Your job is now live on LinkedIn!"
msgstr ""

#: templates/published_job_details.html:629
msgid ""
"Need help? Contact our support team if you encounter any issues during the "
"posting process."
msgstr ""

#: templates/published_job_details.html:646
#, fuzzy
#| msgid "Create Job"
msgid "Current Status:"
msgstr "Créer un Emploi"

#: templates/published_job_details.html:652
msgid "Changing the status will affect the visibility of the vacancy."
msgstr ""

#: templates/published_job_details.html:655
msgid ""
"The vacancy will no longer exist on boards and be closed but be accesible "
"internally."
msgstr ""

#: templates/published_job_details.html:656
msgid "The vacancy will stop accepting new applications until changed."
msgstr ""

#: templates/published_job_details.html:657
msgid "The vacancy will be re-opened for new applications."
msgstr ""

#: templates/published_job_details.html:658
msgid "The vacancy will be permanently deleted. This action cannot be undone."
msgstr ""

#: templates/published_job_details.html:664
#, fuzzy
#| msgid "Search applicants..."
msgid "Select New Status"
msgstr "Rechercher des candidats..."

#: templates/published_job_details.html:687
msgid "Confirm Status"
msgstr ""

#: templates/register.html:11
msgid "Accept Invitation"
msgstr ""

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr ""

#: templates/register.html:18
msgid "Hello"
msgstr ""

#: templates/register.html:18
msgid "you've been invited to join"
msgstr ""

#: templates/register.html:18
msgid "as a"
msgstr ""

#: templates/register.html:29
#, fuzzy
#| msgid "Create Job"
msgid "Create Password"
msgstr "Créer un Emploi"

#: templates/register.html:34
msgid "Confirm Password"
msgstr ""

#: templates/register.html:39
msgid "Complete Registration"
msgstr ""

#: templates/register.html:58
msgid "Passwords do not match"
msgstr ""

#: templates/registration_complete.html:14
msgid "Registration Complete!"
msgstr ""

#: templates/registration_complete.html:15
msgid ""
"Your account has been created successfully. You can now log in to access the "
"system."
msgstr ""

#: templates/registration_complete.html:17
msgid "Go to Login"
msgstr ""

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr ""
"Configurez votre flux de travail de recrutement et gérez les paramètres de "
"votre ATS"

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr ""

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr ""

#: templates/settings.html:24
msgid "Set up office locations"
msgstr ""

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr ""

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr ""

#: templates/settings.html:29
#, fuzzy
#| msgid "Preferences"
msgid "Manage Preferences"
msgstr "Préférences"

#: templates/settings.html:42
#, fuzzy
#| msgid "Configure standard options to streamline your job creation process"
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr ""
"Configurez les options standard pour rationaliser votre processus de "
"création d'emplois"

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr ""

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr ""

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr ""

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr ""

#: templates/settings.html:50
msgid "Manage Templates"
msgstr ""

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr ""

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr ""

#: templates/settings.html:66
msgid "Set user permissions"
msgstr ""

#: templates/settings.html:67
msgid "Track invitation status"
msgstr ""

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr ""

#: templates/settings.html:71
#, fuzzy
#| msgid "Language Settings"
msgid "Manage Invitations"
msgstr "Paramètres de Langue"

#: templates/settings.html:84
msgid "Job Portals"
msgstr ""

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr ""

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr ""

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr ""

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr ""

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr ""

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr ""

#: templates/settings.html:106
msgid "Careers Page & Workloupe Configurations"
msgstr ""

#: templates/settings.html:107
msgid ""
"Adjust the look and feel of your company careers page and workloupe profile "
"to match your branding."
msgstr ""

#: templates/settings.html:109
msgid "Upload company photos"
msgstr ""

#: templates/settings.html:110
msgid "Pick your colours"
msgstr ""

#: templates/settings.html:111
#, fuzzy
#| msgid "Applicants"
msgid "Choose the components"
msgstr "Candidats"

#: templates/settings.html:112
msgid "Upload your company logo and banner"
msgstr ""

#: templates/settings.html:115
msgid "Manage Carees Page & Workloupe Configurations"
msgstr ""

#: templates/settings.html:126
msgid "Need Help?"
msgstr ""

#: templates/settings.html:127
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr ""

#: templates/settings.html:128
msgid "Contact Support"
msgstr ""

#: templates/signin.html:9
msgid "Welcome Back"
msgstr ""

#: templates/signin.html:10
msgid "Sign in to your account"
msgstr ""

#: templates/signin.html:24
msgid "Enter your email"
msgstr ""

#: templates/signin.html:38
msgid "Password"
msgstr ""

#: templates/signin.html:45
msgid "Enter your password"
msgstr ""

#: templates/signin.html:72
msgid "Security Check"
msgstr ""

#: templates/signin.html:72
msgid "What is"
msgstr ""

#: templates/signin.html:79
msgid "Enter the answer"
msgstr ""

#: templates/signin.html:89
msgid "Remember me"
msgstr ""

#: templates/signin.html:92
#, fuzzy
#| msgid "Create Job"
msgid "Forgot password?"
msgstr "Créer un Emploi"

#: templates/signin.html:106
msgid "Don't have an account?"
msgstr ""

#: templates/signin.html:108
msgid "Contact us"
msgstr ""

#: templates/wordpress_integration.html:14
msgid "WordPress Integration"
msgstr ""

#: templates/wordpress_integration.html:15
msgid "Configure your WordPress careers page"
msgstr ""

#: templates/wordpress_integration.html:23
msgid "WordPress Setup"
msgstr ""

#: templates/wordpress_integration.html:28
#, fuzzy
#| msgid "Choose your preferred language for the application interface"
msgid "Choose your preferred WordPress integration method"
msgstr "Choisissez votre langue préférée pour l'interface de l'application"

#: templates/wordpress_integration.html:32
msgid "Integration Method"
msgstr ""

#: templates/wordpress_integration.html:34
msgid "Shortcode (Recommended)"
msgstr ""

#: templates/wordpress_integration.html:35
#: templates/wordpress_integration.html:187
msgid "WordPress Widget"
msgstr ""

#: templates/wordpress_integration.html:36
#: templates/wordpress_integration.html:201
msgid "Custom Plugin"
msgstr ""

#: templates/wordpress_integration.html:63
#, fuzzy
#| msgid "Settings"
msgid "Design Settings"
msgstr "Paramètres"

#: templates/wordpress_integration.html:72
msgid "WordPress Theme Style"
msgstr ""

#: templates/wordpress_integration.html:74
msgid "Inherit from Theme"
msgstr ""

#: templates/wordpress_integration.html:84
msgid "Responsive Design"
msgstr ""

#: templates/wordpress_integration.html:97
msgid "Jobs Per Page"
msgstr ""

#: templates/wordpress_integration.html:109
msgid "Show Job Filters"
msgstr ""

#: templates/wordpress_integration.html:116
msgid "Show Search Box"
msgstr ""

#: templates/wordpress_integration.html:123
msgid "Show Pagination"
msgstr ""

#: templates/wordpress_integration.html:132
msgid "Generate WordPress Code"
msgstr ""

#: templates/wordpress_integration.html:141
msgid "Preview & Instructions"
msgstr ""

#: templates/wordpress_integration.html:144
msgid "Preview"
msgstr ""

#: templates/wordpress_integration.html:147
#, fuzzy
#| msgid "Locations"
msgid "Instructions"
msgstr "Emplacements"

#: templates/wordpress_integration.html:167
#, fuzzy
#| msgid "Locations"
msgid "Shortcode Integration"
msgstr "Emplacements"

#: templates/wordpress_integration.html:169
msgid "Recommended Method"
msgstr ""

#: templates/wordpress_integration.html:169
msgid "Easy to use and works with any WordPress theme"
msgstr ""

#: templates/wordpress_integration.html:172
msgid "Copy the shortcode below"
msgstr ""

#: templates/wordpress_integration.html:173
msgid "Go to your WordPress admin panel"
msgstr ""

#: templates/wordpress_integration.html:174
msgid "Edit the page where you want to display jobs"
msgstr ""

#: templates/wordpress_integration.html:175
msgid "Paste the shortcode in the content area"
msgstr ""

#: templates/wordpress_integration.html:176
msgid "Save and publish the page"
msgstr ""

#: templates/wordpress_integration.html:189
msgid "Perfect for sidebars and widget areas"
msgstr ""

#: templates/wordpress_integration.html:192
msgid "Go to Appearance > Widgets in your WordPress admin"
msgstr ""

#: templates/wordpress_integration.html:193
msgid "Find the 'Workloupe Careers' widget"
msgstr ""

#: templates/wordpress_integration.html:194
msgid "Drag it to your desired widget area"
msgstr ""

#: templates/wordpress_integration.html:195
msgid "Configure the widget settings"
msgstr ""

#: templates/wordpress_integration.html:196
msgid "Save the widget"
msgstr ""

#: templates/wordpress_integration.html:203
msgid "Advanced option - requires technical knowledge"
msgstr ""

#: templates/wordpress_integration.html:206
msgid "Download the custom plugin file"
msgstr ""

#: templates/wordpress_integration.html:207
msgid "Upload it to your WordPress plugins directory"
msgstr ""

#: templates/wordpress_integration.html:208
msgid "Activate the plugin in WordPress admin"
msgstr ""

#: templates/wordpress_integration.html:209
msgid "Configure the plugin settings"
msgstr ""

#: templates/wordpress_integration.html:210
msgid "Use shortcodes or widgets as needed"
msgstr ""

#: templates/wordpress_integration.html:214
msgid "Download Plugin"
msgstr ""

#: templates/wordpress_integration.html:231
msgid "WordPress Integration Code"
msgstr ""

#: templates/wordpress_integration.html:238
msgid "Copy the code below and follow the integration instructions."
msgstr ""

#: templates/wordpress_integration.html:243
msgid "WordPress Code"
msgstr ""

#: templates/wordpress_integration.html:256
msgid "Download Files"
msgstr ""

#: templates/workloupe_platform.html:13
msgid "Workloupe Platform Setup"
msgstr ""

#: templates/workloupe_platform.html:14
msgid "Create and manage your company profile on workloupe.com"
msgstr ""

#: templates/workloupe_platform.html:18
msgid ""
"Update your existing profile information below. All changes will be saved "
"automatically."
msgstr ""

#: templates/workloupe_platform.html:36
msgid "Company name is required"
msgstr ""

#: templates/workloupe_platform.html:41
msgid "Company Email"
msgstr ""

#: templates/workloupe_platform.html:43
msgid "Please enter a valid email address"
msgstr ""

#: templates/workloupe_platform.html:51
msgid "Phone Number"
msgstr ""

#: templates/workloupe_platform.html:53
msgid "Include country code (e.g., ******-123-4567)"
msgstr ""

#: templates/workloupe_platform.html:58
msgid "Website"
msgstr ""

#: templates/workloupe_platform.html:60
msgid "Please enter a valid website URL"
msgstr ""

#: templates/workloupe_platform.html:66
msgid "Company Address"
msgstr ""

#: templates/workloupe_platform.html:67
msgid "Full company address including city, state, country"
msgstr ""

#: templates/workloupe_platform.html:71
#, fuzzy
#| msgid "Locations"
msgid "Office Locations"
msgstr "Emplacements"

#: templates/workloupe_platform.html:72
msgid "Separate multiple locations with | (e.g., New York | London | Remote)"
msgstr ""

#: templates/workloupe_platform.html:73
msgid "Use | to separate multiple office locations"
msgstr ""

#: templates/workloupe_platform.html:77
#, fuzzy
#| msgid "Create Job"
msgid "Company Description"
msgstr "Créer un Emploi"

#: templates/workloupe_platform.html:78
msgid ""
"Describe your company, mission, values, and what makes it special. This will "
"be prominently displayed on your profile."
msgstr ""

#: templates/workloupe_platform.html:79
msgid "Company description is required"
msgstr ""

#: templates/workloupe_platform.html:80
msgid "Minimum 50 characters recommended for better visibility"
msgstr ""

#: templates/workloupe_platform.html:88
msgid "Company Details"
msgstr ""

#: templates/workloupe_platform.html:94
msgid "Industry"
msgstr ""

#: templates/workloupe_platform.html:96
#, fuzzy
#| msgid "Search applicants..."
msgid "Select Industry"
msgstr "Rechercher des candidats..."

#: templates/workloupe_platform.html:97
msgid "Technology"
msgstr ""

#: templates/workloupe_platform.html:98
msgid "Healthcare"
msgstr ""

#: templates/workloupe_platform.html:99
msgid "Finance"
msgstr ""

#: templates/workloupe_platform.html:100
#, fuzzy
#| msgid "Locations"
msgid "Education"
msgstr "Emplacements"

#: templates/workloupe_platform.html:101
msgid "Manufacturing"
msgstr ""

#: templates/workloupe_platform.html:102
msgid "Retail"
msgstr ""

#: templates/workloupe_platform.html:103
msgid "Consulting"
msgstr ""

#: templates/workloupe_platform.html:104
msgid "Marketing & Advertising"
msgstr ""

#: templates/workloupe_platform.html:105
msgid "Real Estate"
msgstr ""

#: templates/workloupe_platform.html:106
msgid "Non-profit"
msgstr ""

#: templates/workloupe_platform.html:113
msgid "Company Size"
msgstr ""

#: templates/workloupe_platform.html:114
msgid "Number of employees"
msgstr ""

#: templates/workloupe_platform.html:115
msgid "Please enter a valid number of employees"
msgstr ""

#: templates/workloupe_platform.html:116
msgid "Enter the total number of employees in your company"
msgstr ""

#: templates/workloupe_platform.html:122
msgid "Social Media Links"
msgstr ""

#: templates/workloupe_platform.html:122
msgid "Optional"
msgstr ""

#: templates/workloupe_platform.html:128
msgid "Please enter a valid LinkedIn URL"
msgstr ""

#: templates/workloupe_platform.html:133
msgid "Please enter a valid Instagram URL"
msgstr ""

#: templates/workloupe_platform.html:140
msgid "Please enter a valid Twitter URL"
msgstr ""

#: templates/workloupe_platform.html:145
msgid "Please enter a valid Github URL"
msgstr ""

#: templates/workloupe_platform.html:152
msgid "Please enter a valid Facebook URL"
msgstr ""

#: templates/workloupe_platform.html:157
msgid "Please enter a valid Glassdoor URL"
msgstr ""

#: templates/workloupe_platform.html:161
msgid ""
"Add your company's social media profiles to increase visibility. URLs will "
"be validated automatically."
msgstr ""

#: templates/workloupe_platform.html:169
msgid "Branding Assets"
msgstr ""

#: templates/workloupe_platform.html:169
msgid "(Publicly Visible)"
msgstr ""

#: templates/workloupe_platform.html:177
msgid "Recommended: 300x300px, PNG or JPG. Max 3MB."
msgstr ""

#: templates/workloupe_platform.html:181
msgid "Current logo - upload new file to replace"
msgstr ""

#: templates/workloupe_platform.html:185 templates/workloupe_platform.html:1165
msgid "No logo uploaded"
msgstr ""

#: templates/workloupe_platform.html:193
msgid "Company Banner"
msgstr ""

#: templates/workloupe_platform.html:195
msgid "Recommended: 1200x400px, PNG or JPG. Max 3MB."
msgstr ""

#: templates/workloupe_platform.html:199
msgid "Current banner - upload new file to replace"
msgstr ""

#: templates/workloupe_platform.html:203 templates/workloupe_platform.html:1172
msgid "No banner uploaded"
msgstr ""

#: templates/workloupe_platform.html:213
msgid ""
"If you don't have online versions of your logo or banner, you can upload "
"them here. They will be stored securely and made publicly accessible for "
"your profile."
msgstr ""

#: templates/workloupe_platform.html:221
msgid "Company Gallery"
msgstr ""

#: templates/workloupe_platform.html:226
msgid "Important:"
msgstr ""

#: templates/workloupe_platform.html:226
msgid ""
"Photos will be public once you publish your profile. Maximum 50 photos "
"allowed. We reserve the right to remove inappropriate content. Only upload "
"professional, work-appropriate images."
msgstr ""

#: templates/workloupe_platform.html:232
#, fuzzy
#| msgid "Current"
msgid "Current Photos"
msgstr "Actuel"

#: templates/workloupe_platform.html:238
msgid "Loading existing photos..."
msgstr ""

#: templates/workloupe_platform.html:246
msgid "Upload New Photos"
msgstr ""

#: templates/workloupe_platform.html:249
msgid ""
"Select multiple photos (JPG, PNG, WebP). Max 5MB per photo. Showcase your "
"company culture, office, team, events, etc."
msgstr ""

#: templates/workloupe_platform.html:260
msgid "Uploading photos..."
msgstr ""

#: templates/workloupe_platform.html:268
msgid "Reset Form"
msgstr ""

#: templates/workloupe_platform.html:272
msgid "Save & Publish Profile"
msgstr ""

#: templates/workloupe_platform.html:279
msgid ""
"Your data is secure and will only be used for your public company profile"
msgstr ""

#: templates/workloupe_platform.html:295
msgid "Profile Published Successfully!"
msgstr ""

#: templates/workloupe_platform.html:302
msgid "Your company is now live on Workloupe!"
msgstr ""

#: templates/workloupe_platform.html:303
msgid ""
"Your company profile has been created and published on workloupe.com. "
"Candidates can now discover your company and apply to your jobs."
msgstr ""

#: templates/workloupe_platform.html:307
#, fuzzy
#| msgid "Profile"
msgid "Your Profile URL:"
msgstr "Profil"

#: templates/workloupe_platform.html:321
#, fuzzy
#| msgid "Profile"
msgid "Share Your Profile"
msgstr "Profil"

#: templates/workloupe_platform.html:322
msgid "Share this link with candidates and on social media"
msgstr ""

#: templates/workloupe_platform.html:330
#, fuzzy
#| msgid "Applicants"
msgid "Update Anytime"
msgstr "Candidats"

#: templates/workloupe_platform.html:331
msgid "Return to this page to update your profile information"
msgstr ""

#: templates/workloupe_platform.html:341
msgid "View Live Profile"
msgstr ""

#: templates/workloupe_platform.html:825
msgid "New logo selected - will be uploaded on save"
msgstr ""

#: templates/workloupe_platform.html:842
msgid "New banner selected - will be uploaded on save"
msgstr ""

#: templates/workloupe_platform.html:850
msgid "Please select a valid image file (JPG, PNG, WebP)"
msgstr ""

#: templates/workloupe_platform.html:856
msgid "File size must be less than"
msgstr ""

#: templates/workloupe_platform.html:868
#, fuzzy
#| msgid "Locations"
msgid "No existing photos found"
msgstr "Emplacements"

#: templates/workloupe_platform.html:889
msgid "Remove image"
msgstr ""

#: templates/workloupe_platform.html:919
msgid "Failed to remove image. Please try again."
msgstr ""

#: templates/workloupe_platform.html:924
msgid "An error occurred while removing the image."
msgstr ""

#: templates/workloupe_platform.html:953
msgid "You can only upload"
msgstr ""

#: templates/workloupe_platform.html:953
msgid "more photos. Maximum"
msgstr ""

#: templates/workloupe_platform.html:953
msgid "photos allowed."
msgstr ""

#: templates/workloupe_platform.html:1042
msgid "Please fix the validation errors before submitting."
msgstr ""

#: templates/workloupe_platform.html:1097
msgid "Publishing..."
msgstr ""

#: templates/workloupe_platform.html:1119
msgid "Unknown error occurred"
msgstr ""

#: templates/workloupe_platform.html:1124
msgid "An error occurred while publishing your profile. Please try again."
msgstr ""

#: templates/workloupe_platform.html:1153
msgid "Failed to copy URL. Please copy it manually."
msgstr ""

#: templates/workloupe_platform.html:1158
msgid ""
"Are you sure you want to reset the form? All unsaved changes will be lost."
msgstr ""
